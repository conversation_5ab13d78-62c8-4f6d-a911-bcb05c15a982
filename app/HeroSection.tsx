'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function HeroSection() {
  const { t } = useLanguage();
  const [counters, setCounters] = useState({
    users: 0,
    transactions: 0,
    countries: 0,
    volume: 0
  });

  useEffect(() => {
    const animateCounters = () => {
      const duration = 3000;
      const steps = 60;
      const increment = duration / steps;
      
      const targets = { users: 750000, transactions: 15000000, countries: 180, volume: 1500 };
      
      let step = 0;
      const timer = setInterval(() => {
        step++;
        const progress = step / steps;
        
        setCounters({
          users: Math.floor(targets.users * progress),
          transactions: Math.floor(targets.transactions * progress),
          countries: Math.floor(targets.countries * progress),
          volume: Math.floor(targets.volume * progress)
        });
        
        if (step >= steps) {
          clearInterval(timer);
          setCounters(targets);
        }
      }, increment);
    };
    
    animateCounters();
  }, []);

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* 升级的动态背景粒子 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-[600px] h-[600px] bg-purple-500/25 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-80 h-80 bg-indigo-500/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-pink-500/20 rounded-full blur-2xl animate-pulse delay-3000"></div>
        <div className="absolute bottom-1/4 right-1/4 w-60 h-60 bg-cyan-500/20 rounded-full blur-2xl animate-pulse delay-4000"></div>
      </div>
      
      {/* 升级的网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      {/* 新增的光束效果 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-purple-500/20 to-transparent"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="pt-32 pb-16 text-center">
          {/* 升级的标签 */}
          <div className="inline-flex items-center bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-500/30 text-blue-300 px-8 py-4 rounded-full text-sm font-medium mb-10 animate-fade-in shadow-2xl">
            <i className="ri-flashlight-line mr-3 text-lg"></i>
            {t('hero.tagline')}
            <i className="ri-arrow-right-line ml-3 text-lg"></i>
          </div>
          
          {/* 升级的主标题 */}
          <h1 className="text-7xl md:text-8xl lg:text-9xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-10 leading-tight">
            {t('hero.title')}
          </h1>
          
          {/* 升级的副标题 */}
          <p className="text-xl md:text-2xl text-gray-300 max-w-5xl mx-auto mb-14 leading-relaxed">
            {t('hero.subtitle')}
          </p>
          
          {/* 升级的CTA按钮 */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-24">
            <Link href="/register" className="group relative bg-gradient-to-r from-blue-500 to-purple-600 text-white py-5 px-12 rounded-2xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 font-semibold text-lg shadow-2xl hover:shadow-blue-500/30 hover:scale-105 whitespace-nowrap overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <i className="ri-rocket-line mr-3 relative z-10 group-hover:scale-110 transition-transform duration-300"></i>
              <span className="relative z-10">Start Building</span>
            </Link>
            <Link href="/solutions" className="group border border-white/30 text-white py-5 px-12 rounded-2xl hover:bg-white/10 transition-all duration-300 font-semibold text-lg backdrop-blur-sm hover:scale-105 whitespace-nowrap relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <i className="ri-play-line mr-3 relative z-10 group-hover:scale-110 transition-transform duration-300"></i>
              <span className="relative z-10">View Demo</span>
            </Link>
          </div>
          
          {/* 升级的统计数据卡片 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-24">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:bg-white/10 transition-all duration-300 group hover:scale-105">
              <div className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {counters.users.toLocaleString()}+
              </div>
              <div className="text-gray-400 text-sm font-medium">Active Users</div>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:bg-white/10 transition-all duration-300 group hover:scale-105">
              <div className="text-4xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {counters.transactions.toLocaleString()}+
              </div>
              <div className="text-gray-400 text-sm font-medium">Transactions</div>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:bg-white/10 transition-all duration-300 group hover:scale-105">
              <div className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                {counters.countries}+
              </div>
              <div className="text-gray-400 text-sm font-medium">Countries</div>
            </div>
            
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 hover:bg-white/10 transition-all duration-300 group hover:scale-105">
              <div className="text-4xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                ${counters.volume}B+
              </div>
              <div className="text-gray-400 text-sm font-medium">Volume Processed</div>
            </div>
          </div>
        </div>
        
        {/* 升级的浮动信息卡片 */}
        <div className="absolute top-1/4 left-8 hidden lg:block">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl animate-float hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-3">
              <i className="ri-flashlight-line text-blue-400 mr-3 text-xl"></i>
              <span className="text-white font-semibold">Lightning Fast</span>
            </div>
            <p className="text-gray-300 text-sm">Sub-second transactions</p>
          </div>
        </div>
        
        <div className="absolute top-1/3 right-8 hidden lg:block">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl animate-float delay-1000 hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-3">
              <i className="ri-shield-check-line text-green-400 mr-3 text-xl"></i>
              <span className="text-white font-semibold">Bank-grade Security</span>
            </div>
            <p className="text-gray-300 text-sm">Military-grade encryption</p>
          </div>
        </div>
        
        <div className="absolute bottom-1/4 left-16 hidden lg:block">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl animate-float delay-2000 hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-3">
              <i className="ri-global-line text-purple-400 mr-3 text-xl"></i>
              <span className="text-white font-semibold">Global Coverage</span>
            </div>
            <p className="text-gray-300 text-sm">Available worldwide</p>
          </div>
        </div>
        
        <div className="absolute bottom-1/3 right-16 hidden lg:block">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 shadow-2xl animate-float delay-3000 hover:scale-105 transition-all duration-300">
            <div className="flex items-center mb-3">
              <i className="ri-money-dollar-circle-line text-yellow-400 mr-3 text-xl"></i>
              <span className="text-white font-semibold">Low Fees</span>
            </div>
            <p className="text-gray-300 text-sm">Starting from 0.1%</p>
          </div>
        </div>
      </div>
    </section>
  );
}