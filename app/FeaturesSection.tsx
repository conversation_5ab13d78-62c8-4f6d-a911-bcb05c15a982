
'use client';

import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

export default function FeaturesSection() {
  const { t } = useLanguage();
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      icon: 'ri-flashlight-line',
      title: t('features.lightningFast.title'),
      description: t('features.lightningFast.description'),
      stats: [t('features.lightningFast.stats.0'), t('features.lightningFast.stats.1'), t('features.lightningFast.stats.2'), t('features.lightningFast.stats.3')],
      color: 'from-blue-500 to-blue-600',
      image: 'https://images.unsplash.com/photo-*************-c232efbf2373?w=600&h=400&fit=crop&crop=center'
    },
    {
      icon: 'ri-shield-check-line',
      title: t('features.bankGrade.title'),
      description: t('features.bankGrade.description'),
      stats: [t('features.bankGrade.stats.0'), t('features.bankGrade.stats.1'), t('features.bankGrade.stats.2'), t('features.bankGrade.stats.3')],
      color: 'from-green-500 to-green-600',
      image: 'https://images.unsplash.com/photo-*************-4dee2763ff3f?w=600&h=400&fit=crop&crop=center'
    },
    {
      icon: 'ri-global-line',
      title: t('features.globalReach.title'),
      description: t('features.globalReach.description'),
      stats: [t('features.globalReach.stats.0'), t('features.globalReach.stats.1'), t('features.globalReach.stats.2'), t('features.globalReach.stats.3')],
      color: 'from-purple-500 to-purple-600',
      image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=600&h=400&fit=crop&crop=center'
    }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-star-line mr-2"></i>
            {t('features.title')}
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
            {t('features.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('features.subtitle')}
          </p>
        </div>
        
        {/* 功能切换器 */}
        <div className="flex justify-center mb-16">
          <div className="flex bg-gray-100 rounded-2xl p-2">
            {features.map((feature, index) => (
              <button
                key={index}
                onClick={() => setActiveFeature(index)}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 whitespace-nowrap cursor-pointer ${
                  activeFeature === index
                    ? 'bg-white text-blue-600 shadow-lg'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <i className={`${feature.icon} mr-2`}></i>
                {feature.title}
              </button>
            ))}
          </div>
        </div>
        
        {/* 当前功能展示 */}
        <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            <div className="p-12">
              <div className={`w-16 h-16 bg-gradient-to-r ${features[activeFeature].color} rounded-2xl flex items-center justify-center mb-8`}>
                <i className={`${features[activeFeature].icon} text-white text-2xl`}></i>
              </div>
              <h3 className="text-4xl font-bold text-gray-900 mb-6">
                {features[activeFeature].title}
              </h3>
              <p className="text-gray-600 text-lg mb-8 leading-relaxed">
                {features[activeFeature].description}
              </p>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {features[activeFeature].stats[0]}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {features[activeFeature].stats[1]}
                  </div>
                </div>
                <div className="text-center p-6 bg-gradient-to-br from-gray-50 to-purple-50 rounded-2xl">
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {features[activeFeature].stats[2]}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {features[activeFeature].stats[3]}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="relative overflow-hidden">
              <img 
                src={features[activeFeature].image}
                alt={features[activeFeature].title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
            </div>
          </div>
        </div>
        
        {/* 底部技术架构 */}
        <div className="mt-20 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-12">
            Powered by Advanced Technology
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
              <i className="ri-cloud-line text-blue-600 text-3xl mb-4"></i>
              <h4 className="font-bold text-gray-900 mb-2">Cloud Native</h4>
              <p className="text-gray-600 text-sm">Scalable microservices architecture</p>
            </div>
            <div className="p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
              <i className="ri-database-2-line text-green-600 text-3xl mb-4"></i>
              <h4 className="font-bold text-gray-900 mb-2">Real-time Processing</h4>
              <p className="text-gray-600 text-sm">Instant transaction processing</p>
            </div>
            <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
              <i className="ri-shield-keyhole-line text-purple-600 text-3xl mb-4"></i>
              <h4 className="font-bold text-gray-900 mb-2">Multi-sig Security</h4>
              <p className="text-gray-600 text-sm">Advanced cryptographic protection</p>
            </div>
            <div className="p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl">
              <i className="ri-line-chart-line text-orange-600 text-3xl mb-4"></i>
              <h4 className="font-bold text-gray-900 mb-2">AI Analytics</h4>
              <p className="text-gray-600 text-sm">Smart fraud detection</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
