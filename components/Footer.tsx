
'use client';

import Link from 'next/link';
import { useLanguage } from '../contexts/LanguageContext';

export default function Footer() {
  const { t } = useLanguage();

  const footerLinks = {
    product: [
      { name: 'Buy Crypto', href: '/buy-crypto' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Features', href: '#features' },
      { name: 'API Documentation', href: '/developers' }
    ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Careers', href: '/careers' },
      { name: 'Press', href: '/press' },
      { name: 'Contact', href: '/contact' }
    ],
    resources: [
      { name: 'Help Center', href: '/support' },
      { name: 'Blog', href: '/blog' },
      { name: 'Developers', href: '/developers' },
      { name: 'Community', href: '/community' }
    ],
    legal: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Compliance', href: '/compliance' },
      { name: 'Cookie Policy', href: '/cookies' }
    ]
  };

  const socialLinks = [
    { name: 'Twitter', href: '#', icon: 'ri-twitter-x-line' },
    { name: 'LinkedIn', href: '#', icon: 'ri-linkedin-line' },
    { name: 'GitHub', href: '#', icon: 'ri-github-line' },
    { name: 'Discord', href: '#', icon: 'ri-discord-line' },
    { name: 'Telegram', href: '#', icon: 'ri-telegram-line' }
  ];

  return (
    <footer className="bg-white border-t border-gray-200 relative overflow-hidden">
      {/* 顶部CTA区域 */}
      <div className="bg-gradient-to-r from-violet-600 to-blue-600 py-16 relative">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_70%)]"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <h3 className="text-4xl font-bold text-white mb-6">
            {t('footer.cta.title')}
          </h3>
          <p className="text-xl text-violet-100 mb-8 max-w-2xl mx-auto">
            {t('footer.cta.subtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register" className="inline-block bg-white text-violet-600 px-8 py-4 rounded-xl font-medium hover:bg-gray-100 transition-all duration-200 shadow-lg">
              <i className="ri-rocket-line mr-2"></i>
              Get Started Free
            </Link>
            <Link href="/contact" className="inline-block border border-white/30 text-white px-8 py-4 rounded-xl font-medium hover:bg-white/10 transition-all duration-200">
              <i className="ri-customer-service-line mr-2"></i>
              Contact Sales
            </Link>
          </div>
        </div>
      </div>

      {/* 主要页脚内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* 品牌信息 */}
          <div className="lg:col-span-2">
            <Link href="/" className="inline-block mb-6">
              <div className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-blue-600 bg-clip-text text-transparent" style={{ fontFamily: '"Pacifico", serif' }}>
                CryptoPay
              </div>
            </Link>
            <p className="text-gray-600 mb-6 leading-relaxed">
              {t('footer.description')}
            </p>
            <div className="flex gap-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="w-12 h-12 bg-gradient-to-r from-gray-100 to-blue-100 rounded-xl flex items-center justify-center text-gray-600 hover:from-violet-500 hover:to-blue-500 hover:text-white transition-all duration-300 hover:scale-110"
                  aria-label={social.name}
                >
                  <i className={`${social.icon} text-xl`}></i>
                </a>
              ))}
            </div>
          </div>

          {/* 产品 */}
          <div>
            <h4 className="text-lg font-bold text-gray-900 mb-6">Product</h4>
            <ul className="space-y-4">
              {footerLinks.product.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-600 hover:text-violet-600 transition-colors duration-200">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 公司 */}
          <div>
            <h4 className="text-lg font-bold text-gray-900 mb-6">Company</h4>
            <ul className="space-y-4">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-600 hover:text-violet-600 transition-colors duration-200">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 资源 */}
          <div>
            <h4 className="text-lg font-bold text-gray-900 mb-6">Resources</h4>
            <ul className="space-y-4">
              {footerLinks.resources.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-600 hover:text-violet-600 transition-colors duration-200">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 法律 */}
          <div>
            <h4 className="text-lg font-bold text-gray-900 mb-6">Legal</h4>
            <ul className="space-y-4">
              {footerLinks.legal.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-600 hover:text-violet-600 transition-colors duration-200">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="border-t border-gray-200 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-600 text-sm">
              {t('footer.copyright')}
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <span className="flex items-center">
                <i className="ri-shield-check-line mr-1 text-green-500"></i>
                SOC 2 Certified
              </span>
              <span className="flex items-center">
                <i className="ri-lock-line mr-1 text-blue-500"></i>
                PCI DSS Level 1
              </span>
              <span className="flex items-center">
                <i className="ri-file-shield-line mr-1 text-purple-500"></i>
                ISO 27001
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
